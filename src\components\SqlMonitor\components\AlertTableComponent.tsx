import type { TableColumnsType, TableProps } from 'antd';
import { Pagination, Table } from 'antd';
import React from 'react';

import type { TaskAlert } from '../types';
import {
  DEFAULT_PAGINATION,
  PAGE_SIZE_OPTIONS,
  PAGINATION_LOCALE,
  TABLE_SCROLL_CONFIG,
  TABLE_SIZE,
} from '../constants';
import { tableStyles } from '../styles';

interface AlertTableComponentProps {
  columns: TableColumnsType<TaskAlert>;
  data: TaskAlert[];
  loading: boolean;
  total: number;
  pagination: {
    current: number;
    page_size: number;
  };
  rowSelection: any;
  tableScrollY: number;
  onTableChange?: TableProps<TaskAlert>['onChange'];
  onPaginationChange: (page: number, pageSize: number) => void;
}

/**
 * 告警表格主体组件
 * 包含表格和分页功能
 */
export const AlertTableComponent: React.FC<AlertTableComponentProps> = ({
  columns,
  data,
  loading,
  total,
  pagination,
  rowSelection,
  tableScrollY,
  onTableChange,
  onPaginationChange,
}) => {
  return (
    <>
      {/* 表格区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="flex-1 overflow-hidden min-h-0">
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            rowSelection={rowSelection}
            pagination={false}
            size={TABLE_SIZE}
            scroll={{
              ...TABLE_SCROLL_CONFIG,
              y: tableScrollY,
            }}
            onChange={onTableChange}
            className={tableStyles.table}
            showSorterTooltip={false}
            locale={{
              emptyText: '暂无数据',
              filterTitle: '筛选',
              filterConfirm: '确定',
              filterReset: '重置',
              selectAll: '全选当页',
              selectInvert: '反选当页',
              selectionAll: '全选所有',
              sortTitle: '排序',
            }}
          />
        </div>
      </div>

      {/* 分页区域 */}
      <div className={tableStyles.paginationContainer}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.page_size}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
          pageSizeOptions={PAGE_SIZE_OPTIONS}
          onChange={onPaginationChange}
          onShowSizeChange={onPaginationChange}
          className={tableStyles.pagination}
          locale={PAGINATION_LOCALE}
          size="default"
          hideOnSinglePage={false}
          responsive={true}
          showLessItems={false}
        />
      </div>
    </>
  );
};
