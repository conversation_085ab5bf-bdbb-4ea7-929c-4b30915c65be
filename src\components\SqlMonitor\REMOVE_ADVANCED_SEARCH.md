# AlertTable 移除详细查询功能总结

## 移除的功能

### 1. 详细查询按钮 ✅

**移除位置**: `AlertQuickSearchForm` 组件中的详细查询按钮

**修改内容**:
- 移除了 `onAdvancedSearch` 参数和相关按钮
- 移除了 `FilterOutlined` 图标的导入
- 简化了按钮布局，只保留搜索和重置按钮

**修改前**:
```tsx
interface AlertQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: TaskAlertSearchParams) => void;
  onReset: () => void;
  onAdvancedSearch: () => void; // 已移除
}

// 按钮区域包含三个按钮：搜索、重置、详细查询
<Button icon={<FilterOutlined />} onClick={onAdvancedSearch}>
  详细查询
</Button>
```

**修改后**:
```tsx
interface AlertQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: TaskAlertSearchParams) => void;
  onReset: () => void;
  // onAdvancedSearch 已移除
}

// 按钮区域只包含两个按钮：搜索、重置
```

### 2. 详细查询表单组件 ✅

**移除组件**: `AlertAdvancedSearchForm`

**移除内容**:
- 完整的详细查询表单组件
- 相关的接口定义 `AlertAdvancedSearchFormProps`
- 表单验证和提交逻辑

### 3. 详细查询模态框 ✅

**移除组件**: `AlertModalManager`

**移除内容**:
- 整个 `AlertModalManager.tsx` 文件
- 详细查询的 Modal 组件
- 搜索模态框的状态管理

**替换方案**:
- 直接在 `AlertTable` 中使用 `Drawer` 组件
- 只保留编辑抽屉功能

### 4. 相关状态和处理函数 ✅

**移除的状态管理**:
```tsx
// 移除的 useModal 相关代码
const {
  modalState: searchModal,
  showModal: showSearchModal,
  hideModal: hideSearchModal,
} = useModal();

// 移除的详细查询处理函数
const handleAdvancedSearch = useCallback(async (values: TaskAlertSearchParams) => {
  // ... 详细查询逻辑
}, []);
```

**移除的导入**:
- `useModal` hook
- `AlertModalManager` 组件
- `AlertAdvancedSearchForm` 组件

## 修改的文件列表

### 1. 主要组件文件
- **src/components/SqlMonitor/components/AlertTable.tsx**
  - 移除 `useModal` 导入和使用
  - 移除 `handleAdvancedSearch` 函数
  - 移除 `AlertModalManager` 组件使用
  - 直接使用 `Drawer` 组件替代
  - 移除不再使用的 `searchParams` 变量

### 2. 表单组件文件
- **src/components/SqlMonitor/components/forms/AlertSearchForm.tsx**
  - 移除 `AlertAdvancedSearchFormProps` 接口
  - 移除 `AlertAdvancedSearchForm` 组件
  - 移除 `onAdvancedSearch` 参数
  - 移除详细查询按钮
  - 移除 `FilterOutlined` 图标导入

### 3. 删除的文件
- **src/components/SqlMonitor/components/modals/AlertModalManager.tsx** (已删除)

## 保留的功能

### ✅ 快速搜索功能
- 告警名称搜索
- 告警级别筛选
- 告警类型筛选
- 搜索和重置按钮

### ✅ 编辑功能
- 新增告警抽屉
- 编辑告警抽屉
- 基本信息表单

### ✅ 其他功能
- 表格展示
- 分页功能
- 排序和筛选
- 批量删除
- 单个删除

## 简化后的组件结构

```
AlertTable
├── AlertQuickSearchForm (简化版，无详细查询)
├── AlertActionButtons
├── AlertTableComponent
└── Drawer (直接使用，替代 AlertModalManager)
    └── AlertBasicForm
```

## 用户体验变化

### 简化的搜索体验
- **之前**: 快速搜索 + 详细查询两套搜索方式
- **现在**: 只有快速搜索，界面更简洁

### 保持的核心功能
- 所有核心的告警管理功能都保留
- 编辑和新增功能完全保留
- 表格操作功能完全保留

## 代码质量改进

### ✅ 减少复杂性
- 移除了不必要的模态框状态管理
- 简化了组件间的数据传递
- 减少了代码维护成本

### ✅ 提高性能
- 减少了组件渲染开销
- 移除了不必要的状态更新
- 简化了事件处理逻辑

### ✅ 更好的可维护性
- 组件职责更加单一
- 减少了组件间的耦合
- 代码结构更加清晰

## 总结

成功移除了 AlertTable 组件中的详细查询功能，包括：

1. **详细查询按钮** - 从快速搜索表单中移除
2. **详细查询表单** - 完整移除 AlertAdvancedSearchForm 组件
3. **详细查询模态框** - 移除 AlertModalManager，直接使用 Drawer
4. **相关状态管理** - 移除 useModal 和相关处理函数

现在 AlertTable 组件更加简洁，只保留核心的告警管理功能，提供更直观的用户体验。
