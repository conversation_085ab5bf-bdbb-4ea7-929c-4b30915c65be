/**
 * Prettier 代码格式化配置文件
 * 用于统一项目中的代码风格和格式化规则
 * @type {import("prettier").Config}
 */
export default {
  // 语句末尾是否添加分号
  semi: true,

  // 多行时尾随逗号的处理方式
  // 'es5': 在ES5中有效的地方添加尾随逗号（对象、数组等）
  // 'none': 不添加尾随逗号
  // 'all': 尽可能添加尾随逗号（包括函数参数）
  trailingComma: 'es5',

  // 使用单引号而不是双引号
  singleQuote: true,

  // 每行最大字符数，超过此长度会换行
  printWidth: 100,

  // 缩进的空格数
  tabWidth: 2,

  // 使用空格而不是制表符进行缩进
  useTabs: false,

  // 对象字面量中括号内是否添加空格
  // true: { foo: bar }
  // false: {foo: bar}
  bracketSpacing: true,

  // 箭头函数参数的括号处理
  // 'avoid': 单个参数时省略括号 x => x
  // 'always': 总是包含括号 (x) => x
  arrowParens: 'avoid',

  // 行尾序列
  // 'lf': 仅换行符（\n），适用于Unix/Linux/macOS
  // 'crlf': 回车符+换行符（\r\n），适用于Windows
  // 'cr': 仅回车符（\r），适用于旧版Mac
  // 'auto': 保持现有的行尾序列
  endOfLine: 'lf',
};
