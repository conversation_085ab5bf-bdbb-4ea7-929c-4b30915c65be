import { EditOutlined, FilterOutlined } from '@ant-design/icons';
import { Drawer, Modal } from 'antd';
import React from 'react';

import type { TaskAlert, TaskAlertSearchParams } from '../../types';
import { AlertAdvancedSearchForm } from '../forms/AlertSearchForm';
import AlertBasicForm from '../forms/AlertBasicForm';

interface AlertModalManagerProps {
  // 搜索模态框
  searchModal: {
    visible: boolean;
    width?: string | number;
  };
  onSearchModalClose: () => void;
  searchForm: any;
  searchParams: TaskAlertSearchParams;
  onAdvancedSearch: (values: TaskAlertSearchParams) => void;
  onSearchReset: () => void;

  // 编辑抽屉
  editDrawer: {
    visible: boolean;
    width?: string | number;
  };
  onEditDrawerClose: () => void;
  currentRecord: TaskAlert | null;
  onFormSubmit: () => void;
  onFormCancel: () => void;
  onFormReset: () => void;
  submitLoading?: boolean;
  editForm: any;
}

/**
 * 告警模态框管理组件
 * 统一管理所有模态框和抽屉
 */
export const AlertModalManager: React.FC<AlertModalManagerProps> = ({
  searchModal,
  onSearchModalClose,
  searchForm,
  searchParams,
  onAdvancedSearch,
  onSearchReset,
  editDrawer,
  onEditDrawerClose,
  currentRecord,
  onFormSubmit,
  onFormCancel,
  onFormReset,
  submitLoading = false,
  editForm,
}) => {
  return (
    <>
      {/* 详细查询Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <FilterOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={onSearchModalClose}
        footer={null}
        width={searchModal.width}
        className="custom-modal"
      >
        <AlertAdvancedSearchForm
          form={searchForm}
          onSubmit={onAdvancedSearch}
          onReset={onSearchReset}
          searchParams={searchParams}
        />
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑告警' : '新增告警'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={onEditDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <AlertBasicForm
          form={editForm}
          initialData={currentRecord || undefined}
          onSubmit={onFormSubmit}
          onCancel={onFormCancel}
          onReset={onFormReset}
          loading={submitLoading}
        />
      </Drawer>
    </>
  );
};
