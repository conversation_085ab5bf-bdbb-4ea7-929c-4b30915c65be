/**
 * 用户数据项类型定义
 * 用于定义表格中每一行数据的类型
 */
export type DataItem = {
  /**
   * 唯一标识符
   */
  id: string;
  /**
   * 姓名
   */
  name: string;
  /**
   * 年龄
   */
  age: number;
  /**
   * 地址
   */
  address: string;
  /**
   * 标签数组
   */
  tags: string[];
  /**
   * 创建时间
   */
  createTime: string;
};

/**
 * 用户表单数据类型（不包含id和createTime）
 */
export type UserFormData = Omit<DataItem, 'id' | 'createTime'>;

/**
 * 搜索参数类型
 */
export type SearchParams = {
  current?: number;
  pageSize?: number;
  name?: string;
  age?: [number, number];
  address?: string;
  tags?: string[];
};

/**
 * API响应类型
 */
export type ApiResponse<T> = {
  data: T[];
  total: number;
  success: boolean;
  message?: string;
};

/**
 * 表格选择状态类型
 */
export type SelectionState = {
  selectedRowKeys: React.Key[];
  selectedRows: DataItem[];
};

/**
 * Modal状态类型
 */
export type ModalState = {
  visible: boolean;
  loading?: boolean;
};

/**
 * 表单操作类型
 */
export type FormAction = 'add' | 'edit';

/**
 * 更新方式类型
 */
export type UpdateMethod = 'reload' | 'direct';
