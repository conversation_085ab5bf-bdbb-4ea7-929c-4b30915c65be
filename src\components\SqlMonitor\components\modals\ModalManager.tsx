import { EditOutlined, FilterOutlined } from '@ant-design/icons';
import { Drawer, Modal } from 'antd';
import React from 'react';

import type { TaskBasic, TaskBasicSearchParams } from '../../types';
import { AdvancedSearchForm } from '../forms/SearchForm';
import ComplexTaskForm from '../forms/ComplexTaskForm';
import GroupManagementModal from './GroupManagementModal';

interface ModalManagerProps {
  // 搜索模态框
  searchModal: {
    visible: boolean;
    width?: string | number;
  };
  onSearchModalClose: () => void;
  searchForm: any;
  searchParams: TaskBasicSearchParams;
  onAdvancedSearch: (values: TaskBasicSearchParams) => void;
  onSearchReset: () => void;

  // 编辑抽屉
  editDrawer: {
    visible: boolean;
    width?: string | number;
  };
  onEditDrawerClose: () => void;
  currentRecord: TaskBasic | null;
  onFormSubmit: () => void;
  onFormCancel: () => void;
  onFormReset: () => void;

  // 分组管理模态框
  groupModal: {
    visible: boolean;
  };
  onGroupModalClose: () => void;
}

/**
 * 模态框管理组件
 * 统一管理所有模态框和抽屉
 */
export const ModalManager: React.FC<ModalManagerProps> = ({
  searchModal,
  onSearchModalClose,
  searchForm,
  searchParams,
  onAdvancedSearch,
  onSearchReset,
  editDrawer,
  onEditDrawerClose,
  currentRecord,
  onFormSubmit,
  onFormCancel,
  onFormReset,
  groupModal,
  onGroupModalClose,
}) => {
  return (
    <>
      {/* 详细查询Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <FilterOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={onSearchModalClose}
        footer={null}
        width={searchModal.width}
        className="custom-modal"
      >
        <AdvancedSearchForm
          form={searchForm}
          onSubmit={onAdvancedSearch}
          onReset={onSearchReset}
          searchParams={searchParams}
        />
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑任务' : '新增任务'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={onEditDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <ComplexTaskForm
          initialData={currentRecord || undefined}
          onSubmit={onFormSubmit}
          onCancel={onFormCancel}
          onReset={onFormReset}
        />
      </Drawer>

      {/* 分组管理Modal */}
      <GroupManagementModal visible={groupModal.visible} onCancel={onGroupModalClose} />
    </>
  );
};
