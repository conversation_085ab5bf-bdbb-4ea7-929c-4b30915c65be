/**
 * API相关类型定义
 */

/**
 * API响应类型
 */
export type TaskApiResponse<T> = {
  data: T[];
  total: number;
  success: boolean;
  message?: string;
};

/**
 * 删除操作响应类型
 */
export type TaskDeleteResponse = {
  name: string;
  status: boolean;
  total: number;
};

/**
 * 通用API响应类型
 */
export interface ApiResponse<T = any> {
  data: T;
  total?: number;
  success: boolean;
  message?: string;
  code?: number;
}

/**
 * 分页参数类型
 */
export interface PaginationParams {
  current: number;
  page_size: number;
}

/**
 * 排序参数类型
 */
export interface SortParams {
  field?: string;
  order?: 'ascend' | 'descend';
}

/**
 * 筛选参数类型
 */
export interface FilterParams {
  [key: string]: any;
}
