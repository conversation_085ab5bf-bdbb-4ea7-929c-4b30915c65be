import React from 'react';
import { Form, Input, Select, Button, Row, Col, Space } from 'antd';
import { SearchOutlined, ReloadOutlined, FilterOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { TaskAlertSearchParams } from '../../types';
import { formStyles } from '../../styles';

const { Option } = Select;

interface AlertQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: TaskAlertSearchParams) => void;
  onReset: () => void;
  onAdvancedSearch: () => void;
}

interface AlertAdvancedSearchFormProps {
  form: FormInstance;
  onSubmit: (values: TaskAlertSearchParams) => void;
  onReset: () => void;
  searchParams: TaskAlertSearchParams;
}

/**
 * 告警快速搜索表单组件
 */
export const AlertQuickSearchForm: React.FC<AlertQuickSearchFormProps> = ({
  form,
  onSubmit,
  onReset,
  onAdvancedSearch,
}) => {
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div className={formStyles.searchFormContainer}>
      <Form form={form} onFinish={onSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input
                  placeholder="请输入告警名称"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="severity" className="mb-0">
                <Select placeholder="请选择告警级别" allowClear className="w-full rounded-md">
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                  <Option value="critical">严重</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="type" className="mb-0">
                <Select placeholder="请选择告警类型" allowClear className="w-full rounded-md">
                  <Option value="isExist">存在性判断</Option>
                  <Option value="isValue">查询值判断</Option>
                  <Option value="isChange">查询值变化判断</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 justify-end">
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSubmit}
                  className={formStyles.searchButton}
                >
                  搜索
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={onReset}
                  className={formStyles.resetButton}
                >
                  重置
                </Button>
                <Button
                  icon={<FilterOutlined />}
                  onClick={onAdvancedSearch}
                  className={formStyles.advancedSearchButton}
                >
                  详细查询
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

/**
 * 告警详细搜索表单组件
 */
export const AlertAdvancedSearchForm: React.FC<AlertAdvancedSearchFormProps> = ({
  form,
  onSubmit,
  onReset,
  searchParams,
}) => {
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 初始化表单值
  React.useEffect(() => {
    form.setFieldsValue(searchParams);
  }, [searchParams, form]);

  return (
    <div className={formStyles.advancedSearchContainer}>
      <Form form={form} layout="vertical" onFinish={onSubmit} className="w-full">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="告警名称" name="name">
              <Input placeholder="请输入告警名称" allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="告警级别" name="severity">
              <Select placeholder="请选择告警级别" allowClear>
                <Option value="low">低</Option>
                <Option value="medium">中</Option>
                <Option value="high">高</Option>
                <Option value="critical">严重</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="告警类型" name="type">
              <Select placeholder="请选择告警类型" allowClear>
                <Option value="isExist">存在性判断</Option>
                <Option value="isValue">查询值判断</Option>
                <Option value="isChange">查询值变化判断</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="SQL语句" name="sql">
              <Input placeholder="请输入SQL语句关键词" allowClear />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <div className="flex justify-end gap-2">
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleSubmit}>
                搜索
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
