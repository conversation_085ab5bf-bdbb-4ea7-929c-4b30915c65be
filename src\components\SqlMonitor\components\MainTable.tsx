import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, App } from 'antd';
import React, { useCallback, useState, useRef, useEffect } from 'react';

// 导入重构后的模块
import type { TaskBasic, TaskBasicSearchParams } from '../types';
import { DEFAULT_PAGINATION } from '../constants';
import { TaskService } from '../services';
import { useTaskData, useTaskSelection, useTable, useModal, useDrawer } from '../hooks';
import { tableStyles } from '../styles';

// 导入拆分的组件
import { createTableColumns } from './TableColumns';
import { QuickSearchForm } from './forms';
import { ActionButtons } from './ActionButtons';
import { TaskTable } from './TaskTable';
import { ModalManager } from './modals';

interface AntdTableProps {
  contentHeight?: number;
}

/**
 * 任务管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const MainTable: React.FC<AntdTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const {
    data,
    loading,
    total,
    pagination,
    searchParams,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  } = useTaskData({ autoLoad: true });

  const { allSelectedRows, rowSelection, clearSelection, getSelectedCount } = useTaskSelection(
    data,
    {
      crossPage: true,
      onSelectionChange: (selectedKeys, selectedRows) => {
        console.log('选择变化:', { selectedKeys, selectedRows });
      },
    }
  );

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } =
    useTable({ contentHeight });

  const {
    modalState: searchModal,
    showModal: showSearchModal,
    hideModal: hideSearchModal,
  } = useModal();

  const {
    modalState: groupModal,
    showModal: showGroupModal,
    hideModal: hideGroupModal,
  } = useModal();

  const {
    drawerState: editDrawer,
    showDrawer: showEditDrawer,
    hideDrawer: hideEditDrawer,
  } = useDrawer();

  // 当前编辑记录
  const [currentRecord, setCurrentRecord] = useState<TaskBasic | null>(null);

  // 表单实例
  const [searchForm] = Form.useForm();

  // 使用 ref 来避免依赖循环
  const loadDataRef = useRef(loadData);

  useEffect(() => {
    loadDataRef.current = loadData;
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskBasicSearchParams) => {
      console.log('搜索表单数据:', values);

      // 更新搜索参数状态
      updateSearchParams(values);

      // 重置排序和筛选状态
      resetSortAndFilter();

      // 重置选择状态
      clearSelection();

      // 使用搜索参数加载数据
      loadDataRef.current({
        ...values,
        current: 1, // 重置到第一页
      });
    },
    [updateSearchParams, resetSortAndFilter, clearSelection]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    // 重置所有状态
    resetData();
    resetSortAndFilter();
    clearSelection();
    searchForm.resetFields();

    // 重新加载数据
    loadDataRef.current({ current: 1, page_size: DEFAULT_PAGINATION.page_size });
  }, [resetData, resetSortAndFilter, clearSelection, searchForm]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskBasicSearchParams) => {
      updateSearchParams(values);
      updatePagination(1, pagination.page_size);
      resetSortAndFilter();
      hideSearchModal();
      clearSelection();

      // 立即使用第一页和新的搜索条件加载数据
      loadDataRef.current({
        current: 1,
        ...values,
      });
    },
    [
      updateSearchParams,
      updatePagination,
      pagination.page_size,
      resetSortAndFilter,
      hideSearchModal,
      clearSelection,
    ]
  );

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        const res = await TaskService.deleteTask(id);
        message.success(`成功删除任务数量 ${res.total}`);
        refreshData();
      } catch (error) {
        message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    [refreshData, message]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = getSelectedCount();
    if (totalSelected === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${totalSelected} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = Array.from(allSelectedRows.keys()).map(key => Number(key));
          const res = await TaskService.batchDeleteTasks(ids);
          message.success(`成功删除任务数量 ${res.total}`);
          clearSelection();
          refreshData();
        } catch (error) {
          message.error('批量删除失败');
          console.error('批量删除失败:', error);
        }
      },
    });
  }, [getSelectedCount, allSelectedRows, message, clearSelection, refreshData]);

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    hideEditDrawer();
    setCurrentRecord(null);
  }, [hideEditDrawer]);

  // 编辑任务处理
  const handleEdit = useCallback(
    (record: TaskBasic) => {
      setCurrentRecord(record);
      showEditDrawer({
        title: '编辑任务',
        width: '90%',
      });
    },
    [showEditDrawer]
  );

  // 表格列定义
  const columns = createTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <QuickSearchForm
          form={searchForm}
          onSubmit={handleSearchFormSubmit}
          onReset={handleReset}
          onAdvancedSearch={() =>
            showSearchModal({
              title: '详细查询',
              width: 800,
            })
          }
        />

        {/* 操作按钮区域 */}
        <ActionButtons
          selectedCount={getSelectedCount()}
          onAddTask={() => {
            setCurrentRecord(null);
            showEditDrawer({
              title: '新增任务',
              width: '90%',
            });
          }}
          onGroupManage={() =>
            showGroupModal({
              title: '分组管理',
              width: 800,
            })
          }
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <TaskTable
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page, pageSize) => {
            updatePagination(page, pageSize);
            loadDataRef.current({
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 模态框管理 */}
      <ModalManager
        searchModal={searchModal}
        onSearchModalClose={hideSearchModal}
        searchForm={searchForm}
        searchParams={searchParams}
        onAdvancedSearch={handleAdvancedSearch}
        onSearchReset={() => {
          searchForm.resetFields();
          updateSearchParams({});
          hideSearchModal();
        }}
        editDrawer={editDrawer}
        onEditDrawerClose={handleDrawerClose}
        currentRecord={currentRecord}
        onFormSubmit={() => {
          hideEditDrawer();
          setCurrentRecord(null);
          refreshData();
        }}
        onFormCancel={handleDrawerClose}
        onFormReset={() => {
          searchForm.resetFields();
          message.info('表单已重置');
        }}
        groupModal={groupModal}
        onGroupModalClose={hideGroupModal}
      />
    </div>
  );
};

export default MainTable;
