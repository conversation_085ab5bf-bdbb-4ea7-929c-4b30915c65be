import React from 'react';
import { Card, Button, Space, Row, Col } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { DBConnection } from '../../types';
import { formStyles } from '../../styles';

interface DatabaseConfigTabProps {
  dbConnection: DBConnection | null;
  onDbConnectionChange: (dbConnection: DBConnection | null) => void;
  onAddDbConnection: () => void;
  onEditDbConnection: () => void;
  onSelectDbConnection: () => void;
}

/**
 * 数据库连接配置标签页组件
 * 管理数据库连接的配置
 */
const DatabaseConfigTab: React.FC<DatabaseConfigTabProps> = ({
  dbConnection,
  onDbConnectionChange,
  onAddDbConnection,
  onEditDbConnection,
  onSelectDbConnection,
}) => {
  const handleDeleteDbConnection = () => {
    onDbConnectionChange(null);
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="数据库连接配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddDbConnection}>
              新增连接
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectDbConnection}>
              选择连接
            </Button>
          </Space>
        }
      >
        {dbConnection ? (
          <div
            style={{
              padding: '16px',
              background: '#f5f5f5',
              borderRadius: '8px',
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <div>
                  <strong>连接名称:</strong> {dbConnection.name}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>数据库类型:</strong> {dbConnection.db_type.toUpperCase()}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>主机地址:</strong> {dbConnection.host}:{dbConnection.port}
                </div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: '8px' }}>
              <Col span={8}>
                <div>
                  <strong>用户名:</strong> {dbConnection.user}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>数据库:</strong> {dbConnection.database || dbConnection.instance}
                </div>
              </Col>
              <Col span={8}>
                <Space>
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={onEditDbConnection}
                  >
                    编辑
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={handleDeleteDbConnection}
                  >
                    删除
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            暂无数据库连接配置，请点击"新增连接"或"选择连接"添加
          </div>
        )}
      </Card>
    </div>
  );
};

export default DatabaseConfigTab;
