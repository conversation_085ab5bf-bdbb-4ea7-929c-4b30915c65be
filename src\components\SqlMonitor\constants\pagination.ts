/**
 * 分页相关常量配置
 */

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION = {
  current: 1,
  page_size: 15,
} as const;

/**
 * 分页大小选项
 */
export const PAGE_SIZE_OPTIONS = ['15', '20', '30', '50', '100', '500'];

/**
 * 分页本地化配置
 */
export const PAGINATION_LOCALE = {
  items_per_page: '条/页',
  jump_to: '跳至',
  jump_to_confirm: '确定',
  page: '页',
  prev_page: '上一页',
  next_page: '下一页',
  prev_5: '向前 5 页',
  next_5: '向后 5 页',
  prev_3: '向前 3 页',
  next_3: '向后 3 页',
} as const;

/**
 * 表格滚动配置
 */
export const TABLE_SCROLL_CONFIG = {
  x: 1200,
  defaultY: 616,
  minY: 300,
  offsetY: 192, // 减去搜索区域、按钮区域、分页区域等
} as const;
