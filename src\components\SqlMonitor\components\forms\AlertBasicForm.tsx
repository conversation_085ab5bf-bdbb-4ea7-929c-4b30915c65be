import React, { useEffect } from 'react';
import { Form, Input, Select, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { TaskAlert } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

const { Option } = Select;
const { TextArea } = Input;

interface AlertBasicFormProps {
  form: FormInstance;
  initialData?: TaskAlert;
  onSubmit?: (values: any) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 告警基础信息表单组件
 * 包含告警的基本配置信息
 */
const AlertBasicForm: React.FC<AlertBasicFormProps> = ({
  form,
  initialData,
  onSubmit,
  onCancel,
  onReset,
  loading = false,
}) => {
  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        name: initialData.name,
        severity: initialData.severity,
        type: initialData.type,
        sql: initialData.sql,
        values: initialData.values?.join(',') || '',
      });
    }
  }, [initialData, form]);

  // 表单提交处理
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理触发值
      const processedValues = {
        ...values,
        values: values.values ? values.values.split(',').map((v: string) => v.trim()).filter(Boolean) : [],
      };

      onSubmit?.(processedValues);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form form={form} layout="vertical" className="flex-1 overflow-auto">
        <div className={formStyles.tabContent}>
          <Card title="告警基本信息" size="small" className="mb-4">
            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item
                  label="告警名称"
                  name="name"
                  rules={[{ required: true, message: '请输入告警名称' }]}
                >
                  <Input placeholder="请输入告警名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="告警级别"
                  name="severity"
                  rules={[{ required: true, message: '请选择告警级别' }]}
                >
                  <Select placeholder="请选择告警级别">
                    <Option value="low">低</Option>
                    <Option value="medium">中</Option>
                    <Option value="high">高</Option>
                    <Option value="critical">严重</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item
                  label="告警类型"
                  name="type"
                  rules={[{ required: true, message: '请选择告警类型' }]}
                >
                  <Select placeholder="请选择告警类型">
                    <Option value="isExist">存在性判断</Option>
                    <Option value="isValue">查询值判断</Option>
                    <Option value="isChange">查询值变化判断</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item
                  label="SQL语句"
                  name="sql"
                  rules={[{ required: true, message: '请输入SQL语句' }]}
                >
                  <TextArea
                    placeholder="请输入SQL语句"
                    rows={4}
                    showCount
                    maxLength={1000}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item
                  label="触发值"
                  name="values"
                  help="多个值用逗号分隔，如：=0,>=1,<2（仅查询值判断类型需要）"
                >
                  <Input placeholder="请输入触发值，多个值用逗号分隔" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>
            {initialData ? '编辑告警信息' : '创建新告警'}
          </div>
          <div className={formStyles.buttonGroup}>
            <Button
              onClick={onCancel}
              className={`${formStyles.actionButton} ${formStyles.cancelButton}`}
            >
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            <Button
              onClick={handleReset}
              className={`${formStyles.actionButton} ${formStyles.resetButton}`}
            >
              {FORM_BUTTON_TEXT.reset}
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={handleSubmit}
              className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}
            >
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertBasicForm;
