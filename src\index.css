/* @tailwindcss */
@import 'tailwindcss';

/* 自定义样式 - 确保 InputNumber 宽度为 100% */
@layer components {
  .input-number-full {
    width: 100% !important;
  }
}

/* Tailwind v4 原生滚动条样式 */
@layer utilities {
  /* 基础滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) rgb(241 245 249);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(241 245 249);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(203 213 225);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(148 163 184);
  }

  /* 主题色滚动条 */
  .scrollbar-primary {
    scrollbar-color: rgb(59 130 246) rgb(239 246 255);
  }

  .scrollbar-primary::-webkit-scrollbar-track {
    background: rgb(239 246 255);
  }

  .scrollbar-primary::-webkit-scrollbar-thumb {
    background: rgb(59 130 246);
  }

  .scrollbar-primary::-webkit-scrollbar-thumb:hover {
    background: rgb(37 99 235);
  }

  /* 隐藏滚动条 */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* 全局默认滚动条 */
@layer base {
  * {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) rgb(241 245 249);
  }

  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
    background: rgb(241 245 249);
    border-radius: 4px;
  }

  *::-webkit-scrollbar-thumb {
    background: rgb(203 213 225);
    border-radius: 4px;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: rgb(148 163 184);
  }
}

/* 基础重置样式 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html,
  body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
      'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
  }
}
