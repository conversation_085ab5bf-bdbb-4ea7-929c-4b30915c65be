import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import { API_BASE_URL, REQUEST_TIMEOUT } from '@/constants/api';

/**
 * 错误处理函数
 */
const errorHandler = (error: AxiosError) => {
  if (error.response) {
    // 服务器响应了状态码，但不在2xx范围内
    console.error('响应错误:', error.response.data);
    console.error('状态码:', error.response.status);
    console.error('响应头:', error.response.headers);
  } else if (error.request) {
    // 请求已发出，但没有收到响应
    console.error('请求错误:', error.request);
  } else {
    // 在设置请求时发生了错误
    console.error('配置错误:', error.message);
  }
  return Promise.reject(error);
};

/**
 * HTTP客户端封装
 */
class HttpClient {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      config => {
        // 添加认证token等
        return config;
      },
      error => Promise.reject(error)
    );

    // 响应拦截器
    this.instance.interceptors.response.use(response => response.data, errorHandler);
  }

  // GET请求
  get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get<T>(url, config) as Promise<T>;
  }

  // POST请求
  post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post<T>(url, data, config) as Promise<T>;
  }

  // PUT请求
  put<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put<T>(url, data, config) as Promise<T>;
  }

  // DELETE请求
  delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete<T>(url, config) as Promise<T>;
  }
}

export const httpClient = new HttpClient();
