// 频率单位映射
export const FREQUENCY_UNIT_MAP = {
  // 英文到中文
  sec: '秒',
  min: '分',
  hour: '时',
  day: '日',
  // 中文到英文
  秒: 'sec',
  分: 'min',
  时: 'hour',
  日: 'day',
} as const;

// 重试频率单位映射
export const RETRY_FREQUENCY_UNIT_MAP = {
  // 英文到中文
  sec: '秒',
  min: '分钟',
  hour: '小时',
  // 中文到英文
  秒: 'sec',
  分钟: 'min',
  小时: 'hour',
} as const;

/**
 * 将英文格式的频率字符串转换为中文对象格式
 * @param frequencyStr 格式如 "40sec"、"5hour" 等
 * @param isRetryFrequency 是否为重试频率（影响单位映射）
 * @returns 包含 value 和 unit 的对象
 */
export const parseFrequencyFromString = (
  frequencyStr: string,
  isRetryFrequency: boolean = false
): { value: number; unit: string } | null => {
  const match = frequencyStr.match(/^(\d+)(sec|min|hour|day)$/);
  if (!match) return null;

  const [, value, unit] = match;
  const unitMap = isRetryFrequency ? RETRY_FREQUENCY_UNIT_MAP : FREQUENCY_UNIT_MAP;
  const chineseUnit = unitMap[unit as keyof typeof unitMap];

  return {
    value: parseInt(value),
    unit: chineseUnit || (isRetryFrequency ? '分钟' : '分'),
  };
};

/**
 * 将中文对象格式的频率转换为英文字符串格式
 * @param frequency 包含 value 和 unit 的对象
 * @param isRetryFrequency 是否为重试频率（影响单位映射）
 * @returns 格式如 "40sec"、"5hour" 等的字符串
 */
export const formatFrequencyToString = (
  frequency: { value: number; unit: string },
  isRetryFrequency: boolean = false
): string => {
  const { value, unit } = frequency;
  const unitMap = isRetryFrequency ? RETRY_FREQUENCY_UNIT_MAP : FREQUENCY_UNIT_MAP;
  const englishUnit = unitMap[unit as keyof typeof unitMap];

  return `${value}${englishUnit || (isRetryFrequency ? 'min' : 'min')}`;
};
