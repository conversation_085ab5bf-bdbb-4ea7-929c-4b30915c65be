# AlertTable 组件开发总结

## 概述

根据您的要求，我已经成功创建了 `AlertTable` 组件，它与 `MainTable` 基本相同，但有以下关键差异：

1. **移除了分组按钮**
2. **编辑和新增抽屉只显示基本信息部分**
3. **表格数据使用 TaskAlert 类型**

## 创建的文件列表

### 1. 核心组件
- `src/components/SqlMonitor/components/AlertTable.tsx` - 主要的告警表格组件
- `src/components/SqlMonitor/components/AlertTableColumns.tsx` - 告警表格列配置
- `src/components/SqlMonitor/components/AlertTableComponent.tsx` - 告警表格主体组件
- `src/components/SqlMonitor/components/AlertActionButtons.tsx` - 告警操作按钮（无分组管理）

### 2. 表单组件
- `src/components/SqlMonitor/components/forms/AlertBasicForm.tsx` - 告警基本信息表单
- `src/components/SqlMonitor/components/forms/AlertSearchForm.tsx` - 告警搜索表单

### 3. 模态框组件
- `src/components/SqlMonitor/components/modals/AlertModalManager.tsx` - 告警模态框管理器

### 4. Hooks
- `src/components/SqlMonitor/hooks/useAlertData.ts` - 告警数据管理Hook
- `src/components/SqlMonitor/hooks/useAlertTable.ts` - 告警表格管理Hook

### 5. 类型定义
- 在 `src/components/SqlMonitor/types/task.ts` 中添加了 `TaskAlertSearchParams` 类型

### 6. 服务方法
- 在 `src/components/SqlMonitor/services/taskService.ts` 中添加了告警删除相关方法

### 7. 示例文件
- `src/components/SqlMonitor/examples/AlertTableExample.tsx` - 使用示例

## 主要功能特点

### 1. 表格功能
- ✅ 告警列表展示（告警名称、级别、类型、SQL语句、触发值、创建时间）
- ✅ 排序功能（支持多列排序）
- ✅ 筛选功能（支持搜索和下拉选择筛选）
- ✅ 分页功能
- ✅ 跨页选择功能
- ✅ 批量删除功能

### 2. 搜索功能
- ✅ 快速搜索（告警名称、级别、类型）
- ✅ 详细搜索（支持更多搜索条件）
- ✅ 搜索重置功能

### 3. 操作功能
- ✅ 新增告警（只显示基本信息表单）
- ✅ 编辑告警（只显示基本信息表单）
- ✅ 删除单个告警
- ✅ 批量删除告警
- ❌ 分组管理（已移除）

### 4. 表单功能
- ✅ 告警基本信息编辑
  - 告警名称（必填）
  - 告警级别（低/中/高/严重）
  - 告警类型（存在性判断/查询值判断/查询值变化判断）
  - SQL语句（必填）
  - 触发值（可选，多个值用逗号分隔）

## 与 MainTable 的差异

### 1. 数据类型
- **MainTable**: 使用 `TaskBasic` 类型
- **AlertTable**: 使用 `TaskAlert` 类型

### 2. 表格列
- **MainTable**: 任务名称、分组、执行时间、星期、频率、状态、重试次数、重试间隔
- **AlertTable**: 告警名称、级别、类型、SQL语句、触发值、创建时间

### 3. 操作按钮
- **MainTable**: 新增任务 + 分组管理
- **AlertTable**: 新增告警（无分组管理）

### 4. 编辑表单
- **MainTable**: 完整的多标签页表单（基本信息、告警配置、数据库连接、告警发送、其他信息）
- **AlertTable**: 简化的单页表单（仅基本信息）

## 使用方法

```tsx
import { AlertTable } from '@/components/SqlMonitor';

// 基本使用
<AlertTable />

// 指定内容高度
<AlertTable contentHeight={800} />
```

## 技术实现

### 1. 状态管理
- 使用 `useAlertData` 管理告警数据的加载、搜索、分页
- 使用 `useAlertTable` 管理表格的排序、筛选状态
- 使用 `useTaskSelection` 管理行选择状态
- 使用 `useModal` 和 `useDrawer` 管理模态框和抽屉状态

### 2. 类型安全
- 完整的 TypeScript 类型定义
- 泛型支持，确保类型安全

### 3. 组件复用
- 复用了部分现有组件（如 `useTaskSelection`）
- 创建了专门的告警相关组件，避免类型冲突

### 4. 数据模拟
- 复用现有的模拟数据服务
- 支持搜索、分页、排序的模拟实现

## 注意事项

1. **表单验证**: 告警名称和SQL语句为必填项
2. **触发值**: 仅在"查询值判断"类型时需要填写
3. **数据持久化**: 当前使用模拟数据，实际使用时需要连接真实API
4. **权限控制**: 当前未实现权限控制，可根据需要添加

## 后续扩展

如需要添加更多功能，可以考虑：

1. **导入导出**: 支持告警配置的导入导出
2. **模板功能**: 支持告警模板的创建和使用
3. **历史记录**: 支持查看告警配置的修改历史
4. **批量编辑**: 支持批量修改告警配置
5. **复制功能**: 支持复制现有告警配置

## 总结

AlertTable 组件已经完全实现了您的要求，提供了完整的告警管理功能，同时保持了与 MainTable 相似的用户体验。组件具有良好的类型安全性、可维护性和可扩展性。
