/**
 * 任务选择管理Hook
 * 管理表格行选择状态，支持跨页面选择
 */

import { useState, useCallback, useEffect } from 'react';
import type { TableRowSelection } from 'antd/es/table/interface';
import type { TaskBasic, TaskSelectionState } from '../types';

export interface UseTaskSelectionOptions {
  /** 是否支持跨页面选择 */
  crossPage?: boolean;
  /** 选择变化回调 */
  onSelectionChange?: (selectedKeys: React.Key[], selectedRows: TaskBasic[]) => void;
}

export interface UseTaskSelectionReturn {
  /** 当前页面选择状态 */
  selection: TaskSelectionState;
  /** 所有选中的行数据（跨页面） */
  allSelectedRows: Map<React.Key, TaskBasic>;
  /** 表格行选择配置 */
  rowSelection: TableRowSelection<TaskBasic>;
  /** 清空选择方法 */
  clearSelection: () => void;
  /** 选择所有方法 */
  selectAll: (data: TaskBasic[]) => void;
  /** 取消选择所有方法 */
  unselectAll: (data: TaskBasic[]) => void;
  /** 获取选中的行数据方法 */
  getSelectedRows: () => TaskBasic[];
  /** 获取选中的行键方法 */
  getSelectedKeys: () => React.Key[];
  /** 获取选中数量方法 */
  getSelectedCount: () => number;
}

/**
 * 任务选择管理Hook
 */
export function useTaskSelection(
  data: TaskBasic[],
  options: UseTaskSelectionOptions = {}
): UseTaskSelectionReturn {
  const { crossPage = true, onSelectionChange } = options;

  // 当前页面选择状态
  const [selection, setSelection] = useState<TaskSelectionState>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 存储所有已选择的行数据（跨页面）
  const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, TaskBasic>>(new Map());

  // 更新当前页面的选择状态
  useEffect(() => {
    if (crossPage) {
      // 根据全局选择状态更新当前页面的选择
      const currentPageSelectedKeys = data
        .filter(item => allSelectedRows.has(item.id))
        .map(item => item.id);
      const currentPageSelectedRows = data.filter(item => allSelectedRows.has(item.id));

      setSelection({
        selectedRowKeys: currentPageSelectedKeys,
        selectedRows: currentPageSelectedRows,
      });
    }
  }, [data, allSelectedRows, crossPage]);

  // 表格行选择配置
  const rowSelection: TableRowSelection<TaskBasic> = {
    selectedRowKeys: selection.selectedRowKeys,
    onChange: useCallback(
      (selectedRowKeys: React.Key[], selectedRows: TaskBasic[]) => {
        // 更新当前页面的选择状态
        setSelection({
          selectedRowKeys,
          selectedRows,
        });

        if (crossPage) {
          // 更新全局选择状态
          const newAllSelectedRows = new Map(allSelectedRows);

          // 移除当前页面中未选中的项
          data.forEach(item => {
            if (!selectedRowKeys.includes(item.id)) {
              newAllSelectedRows.delete(item.id);
            }
          });

          // 添加当前页面中新选中的项
          selectedRows.forEach(row => {
            newAllSelectedRows.set(row.id, row);
          });

          setAllSelectedRows(newAllSelectedRows);

          // 触发回调
          onSelectionChange?.(
            Array.from(newAllSelectedRows.keys()),
            Array.from(newAllSelectedRows.values())
          );
        } else {
          // 不支持跨页面选择时，直接使用当前页面的选择
          onSelectionChange?.(selectedRowKeys, selectedRows);
        }
      },
      [data, allSelectedRows, crossPage, onSelectionChange]
    ),
    onSelectAll: useCallback(
      (selected: boolean) => {
        if (crossPage) {
          const newAllSelectedRows = new Map(allSelectedRows);

          if (selected) {
            // 全选当前页面
            data.forEach(row => {
              newAllSelectedRows.set(row.id, row);
            });
          } else {
            // 取消选择当前页面
            data.forEach(row => {
              newAllSelectedRows.delete(row.id);
            });
          }

          setAllSelectedRows(newAllSelectedRows);

          // 更新当前页面选择状态
          const currentPageKeys = selected ? data.map(item => item.id) : [];
          const currentPageRows = selected ? data : [];
          setSelection({
            selectedRowKeys: currentPageKeys,
            selectedRows: currentPageRows,
          });

          // 触发回调
          onSelectionChange?.(
            Array.from(newAllSelectedRows.keys()),
            Array.from(newAllSelectedRows.values())
          );
        } else {
          // 不支持跨页面选择时
          const selectedKeys = selected ? data.map(item => item.id) : [];
          const selectedRows = selected ? data : [];

          setSelection({
            selectedRowKeys: selectedKeys,
            selectedRows: selectedRows,
          });

          onSelectionChange?.(selectedKeys, selectedRows);
        }
      },
      [data, allSelectedRows, crossPage, onSelectionChange]
    ),
  };

  // 清空选择方法
  const clearSelection = useCallback(() => {
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
    setAllSelectedRows(new Map());
    onSelectionChange?.([], []);
  }, [onSelectionChange]);

  // 选择所有方法
  const selectAll = useCallback(
    (currentData: TaskBasic[]) => {
      if (crossPage) {
        const newAllSelectedRows = new Map(allSelectedRows);
        currentData.forEach(row => {
          newAllSelectedRows.set(row.id, row);
        });
        setAllSelectedRows(newAllSelectedRows);
        onSelectionChange?.(
          Array.from(newAllSelectedRows.keys()),
          Array.from(newAllSelectedRows.values())
        );
      } else {
        const selectedKeys = currentData.map(item => item.id);
        setSelection({
          selectedRowKeys: selectedKeys,
          selectedRows: currentData,
        });
        onSelectionChange?.(selectedKeys, currentData);
      }
    },
    [allSelectedRows, crossPage, onSelectionChange]
  );

  // 取消选择所有方法
  const unselectAll = useCallback(
    (currentData: TaskBasic[]) => {
      if (crossPage) {
        const newAllSelectedRows = new Map(allSelectedRows);
        currentData.forEach(row => {
          newAllSelectedRows.delete(row.id);
        });
        setAllSelectedRows(newAllSelectedRows);
        onSelectionChange?.(
          Array.from(newAllSelectedRows.keys()),
          Array.from(newAllSelectedRows.values())
        );
      } else {
        setSelection({
          selectedRowKeys: [],
          selectedRows: [],
        });
        onSelectionChange?.([], []);
      }
    },
    [allSelectedRows, crossPage, onSelectionChange]
  );

  // 获取选中的行数据方法
  const getSelectedRows = useCallback((): TaskBasic[] => {
    return crossPage ? Array.from(allSelectedRows.values()) : selection.selectedRows;
  }, [crossPage, allSelectedRows, selection.selectedRows]);

  // 获取选中的行键方法
  const getSelectedKeys = useCallback((): React.Key[] => {
    return crossPage ? Array.from(allSelectedRows.keys()) : selection.selectedRowKeys;
  }, [crossPage, allSelectedRows, selection.selectedRowKeys]);

  // 获取选中数量方法
  const getSelectedCount = useCallback((): number => {
    return crossPage ? allSelectedRows.size : selection.selectedRows.length;
  }, [crossPage, allSelectedRows.size, selection.selectedRows.length]);

  return {
    selection,
    allSelectedRows,
    rowSelection,
    clearSelection,
    selectAll,
    unselectAll,
    getSelectedRows,
    getSelectedKeys,
    getSelectedCount,
  };
}
