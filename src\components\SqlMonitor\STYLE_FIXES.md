# AlertTable 样式和高度修复总结

## 修复的问题

### 1. 高度计算不一致 ✅

**问题**: `useAlertTable` 和 `useTable` 使用不同的高度计算方式

**原始问题**:
- `useAlertTable`: 使用硬编码的预留高度计算
- `useTable`: 使用 `TABLE_SCROLL_CONFIG.offsetY` 配置

**修复方案**:
```typescript
// 修复前 (useAlertTable.ts)
const reservedHeight = 64 + 60 + 48 + 56 + 32;
const calculatedHeight = contentHeight - reservedHeight;
const finalHeight = Math.max(minHeight, Math.min(maxHeight, calculatedHeight));

// 修复后 (useAlertTable.ts)
const calculatedScrollY = contentHeight - TABLE_SCROLL_CONFIG.offsetY;
setTableScrollY(Math.max(calculatedScrollY, TABLE_SCROLL_CONFIG.minY));
```

### 2. 搜索表单样式不一致 ✅

**问题**: `AlertQuickSearchForm` 和 `QuickSearchForm` 使用不同的布局和样式

**主要差异**:
1. **外层容器样式**:
   - 原始: `className={tableStyles.searchSection}`
   - 修复前: `className={formStyles.searchFormContainer}`

2. **表单布局**:
   - 原始: 使用 `Row` 和 `Col` 响应式布局
   - 修复前: 使用 `layout="inline"` 内联布局

3. **按钮样式和布局**:
   - 原始: `htmlType="submit"` + `className={tableStyles.buttonPrimary}`
   - 修复前: `onClick={handleSubmit}` + `className={formStyles.searchButton}`

**修复方案**:
```tsx
// 修复后的 AlertQuickSearchForm 结构
<div className={tableStyles.searchSection}>
  <Form form={form} onFinish={onSubmit} className="w-full">
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={8} md={6}>
        <div className="flex flex-col gap-1 h-full justify-center">
          <Form.Item name="name" className="mb-0">
            <Input
              placeholder="请输入告警名称"
              prefix={<SearchOutlined className="text-gray-400" />}
              allowClear
              className="rounded-md"
              // ... 其他属性与原始一致
            />
          </Form.Item>
        </div>
      </Col>
      {/* 其他列... */}
      <Col xs={24} sm={24} md={6}>
        <div className="flex flex-col gap-1 h-full justify-center">
          <div className="flex gap-2 items-center h-full">
            <Button
              type="primary"
              htmlType="submit"
              icon={<SearchOutlined />}
              className={tableStyles.buttonPrimary}
            >
              搜索
            </Button>
            <Button onClick={onReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
              重置
            </Button>
            <Button
              icon={<FilterOutlined />}
              onClick={onAdvancedSearch}
              className="rounded-md flex-1"
            >
              详细查询
            </Button>
          </div>
        </div>
      </Col>
    </Row>
  </Form>
</div>
```

### 3. 分页组件样式不一致 ✅

**问题**: `AlertTableComponent` 和 `TaskTable` 使用不同的分页布局

**原始问题**:
- `AlertTableComponent`: 使用 `tableStyles.paginationContainer`
- `TaskTable`: 使用自定义的分页布局和样式

**修复方案**:
```tsx
// 修复前 (AlertTableComponent)
<div className={tableStyles.paginationContainer}>
  <Pagination
    // ... 配置
    className={tableStyles.pagination}
  />
</div>

// 修复后 (AlertTableComponent)
<div className="flex-shrink-0 h-12 border-t border-gray-200 bg-gray-50 flex items-center">
  <div className="px-4 w-full h-full flex items-center">
    <div className="flex justify-between items-center w-full h-full">
      <div className={tableStyles.dataStats}>
        <span>共 {total} 条数据</span>
      </div>
      <Pagination
        // ... 配置
        className="custom-pagination"
        size="small"
      />
    </div>
  </div>
</div>
```

### 4. 表格样式不一致 ✅

**问题**: 表格组件使用不同的样式类

**修复方案**:
```tsx
// 修复前
className={tableStyles.table}
showSorterTooltip={false}
locale={{ /* 自定义locale */ }}

// 修复后
size={TABLE_SIZE}
className={`custom-table h-full`}
// 移除了 showSorterTooltip 和 locale 配置
```

## 修复的文件列表

1. **src/components/SqlMonitor/hooks/useAlertTable.ts**
   - 修复高度计算逻辑，使用 `TABLE_SCROLL_CONFIG.offsetY`

2. **src/components/SqlMonitor/components/forms/AlertSearchForm.tsx**
   - 修复搜索表单布局和样式
   - 更改外层容器样式类
   - 修复按钮样式和事件处理
   - 更新导入语句

3. **src/components/SqlMonitor/components/AlertTableComponent.tsx**
   - 修复分页组件布局和样式
   - 修复表格样式类
   - 移除不必要的配置

4. **src/components/SqlMonitor/constants/pagination.ts**
   - 移除 `as const` 断言，修复类型问题

## 验证结果

### ✅ 高度一致性
- AlertTable 和 MainTable 现在使用相同的高度计算逻辑
- 表格滚动高度计算一致

### ✅ 搜索表单一致性
- 布局结构完全一致
- 样式类完全一致
- 响应式行为一致

### ✅ 分页组件一致性
- 分页布局完全一致
- 数据统计显示一致
- 分页控件样式一致

### ✅ 表格样式一致性
- 表格大小和样式类一致
- 移除了不必要的配置差异

## 总结

现在 AlertTable 组件的样式和布局与 MainTable 完全一致：

1. **搜索区域**: 使用相同的响应式布局和样式
2. **操作按钮区域**: 高度和样式一致（除了移除分组管理按钮）
3. **表格区域**: 高度计算和样式完全一致
4. **分页区域**: 布局和样式完全一致

所有组件现在都遵循统一的设计规范和样式系统。
