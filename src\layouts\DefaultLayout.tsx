import React, { useState, useMemo, useCallback } from 'react';
import { Layout } from 'antd';
import { Outlet, useLocation } from 'react-router-dom';
import { Header, Sidebar, TabsContainer } from './components';
import { useTabs, useBreadcrumb, useContentHeight, useSelectedKeys } from './hooks';
import { getSidebarMenuItems, getUserMenuItems } from './constants';
import styles from './DefaultLayout.module.css';

const { Content } = Layout;

/**
 * 默认布局组件
 * 提供应用的主要布局结构，包括侧边栏、头部、标签页和内容区域
 */
export const DefaultLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  // 使用自定义 hooks
  const { activeTabKey, tabs, handleTabChange, removeTab, handleMenuClick } = useTabs();
  const breadcrumbItems = useBreadcrumb();
  const { contentHeight, contentRef } = useContentHeight();
  const selectedKeys = useSelectedKeys();

  // 缓存菜单配置，避免每次渲染都重新创建
  const sidebarMenuItems = useMemo(() => getSidebarMenuItems(), []);
  const userMenuItems = useMemo(() => getUserMenuItems(), []);

  // 切换侧边栏折叠状态
  const handleToggleCollapse = useCallback(() => {
    setCollapsed(prev => !prev);
  }, []);

  return (
    <Layout className={styles.layout}>
      {/* 侧边栏 */}
      <Sidebar
        collapsed={collapsed}
        selectedKeys={selectedKeys}
        menuItems={sidebarMenuItems}
        onMenuClick={handleMenuClick}
      />

      {/* 主要内容区域 */}
      <Layout>
        {/* 顶部导航栏 */}
        <Header
          collapsed={collapsed}
          onToggleCollapse={handleToggleCollapse}
          breadcrumbItems={breadcrumbItems}
          userMenuItems={userMenuItems}
        />

        {/* 标签页区域 */}
        <TabsContainer
          activeTabKey={activeTabKey}
          tabs={tabs}
          onTabChange={handleTabChange}
          onTabRemove={removeTab}
        />

        {/* 内容区域 */}
        <Content ref={contentRef} className={styles.content}>
          <div key={location.pathname} className={styles.contentWrapper}>
            <Outlet
              context={{
                contentHeight,
              }}
            />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

// 保持向后兼容
export const ResponsiveLayout = DefaultLayout;
