import React, { useState } from 'react';
import { Button, Card, Space, message } from 'antd';
import { AlertDrawer } from '../components/drawers/AlertDrawer';
import type { TaskAlert } from '../types/task';

const AlertDrawerDemo: React.FC = () => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingData, setEditingData] = useState<TaskAlert | undefined>(undefined);

  const handleOpenDrawer = () => {
    setEditingData(undefined);
    setDrawerVisible(true);
  };

  const handleOpenEditDrawer = () => {
    setEditingData({
      id: 1,
      name: '示例告警',
      severity: 'high',
      sql: 'SELECT COUNT(*) FROM users WHERE status = "active"',
      type: 'greaterThan',
      values: [],
      create_time: new Date().toISOString(),
      update_time: new Date().toISOString(),
    });
    setDrawerVisible(true);
  };

  const handleCloseDrawer = () => {
    setDrawerVisible(false);
    setEditingData(undefined);
  };

  const handleSubmit = (data: TaskAlert) => {
    console.log('提交的告警数据:', data);
    message.success(`${editingData ? '更新' : '创建'}告警成功！`);
    handleCloseDrawer();
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="AlertDrawer 组件演示" style={{ maxWidth: 800 }}>
        <Space>
          <Button type="primary" onClick={handleOpenDrawer}>
            新增告警（抽屉形式）
          </Button>
          <Button onClick={handleOpenEditDrawer}>编辑告警（抽屉形式）</Button>
        </Space>

        <div style={{ marginTop: 16 }}>
          <h3>功能特点：</h3>
          <ul>
            <li>✅ 使用 Drawer 组件替代 Modal，宽度为 60%</li>
            <li>✅ 包含表单配置区域</li>
            <li>✅ 包含表格预览区域，参考分组管理表格布局</li>
            <li>✅ 支持新增和编辑模式</li>
            <li>✅ 表格显示告警信息的预览</li>
            <li>✅ 响应式设计，适配不同屏幕尺寸</li>
          </ul>
        </div>
      </Card>

      <AlertDrawer
        visible={drawerVisible}
        editingData={editingData}
        onCancel={handleCloseDrawer}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default AlertDrawerDemo;
