import React, { useEffect, useState } from 'react';
import { App, ConfigProvider, Card, Space, Button, message } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import AlertTable from '../components/AlertTable';

/**
 * AlertTable 组件示例
 * 展示告警管理表格的基本用法
 */
const AlertTableExample: React.FC = () => {
  const [contentHeight, setContentHeight] = useState(window.innerHeight - 100);

  useEffect(() => {
    const handleResize = () => {
      setContentHeight(window.innerHeight - 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleTest = () => {
    message.info('AlertTable 组件测试成功！');
  };

  return (
    <ConfigProvider locale={zhCN}>
      <App>
        <div style={{ height: '100vh', padding: '16px', backgroundColor: '#f5f5f5' }}>
          <Card style={{ marginBottom: '16px' }}>
            <Space>
              <h1 style={{ margin: 0 }}>告警管理表格示例</h1>
              <Button type="primary" onClick={handleTest}>
                测试组件
              </Button>
            </Space>
          </Card>

          <Card style={{ height: 'calc(100vh - 140px)' }}>
            <AlertTable contentHeight={contentHeight} />
          </Card>
        </div>
      </App>
    </ConfigProvider>
  );
};

export default AlertTableExample;
