/**
 * ComplexTaskForm 使用示例
 * 展示如何使用重构后的复合任务表单组件
 */

import React, { useState } from 'react';
import { Button, Drawer, message } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { ComplexTaskForm } from '../components';
import type { TaskBasic } from '../types';

const ComplexTaskFormExample: React.FC = () => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskBasic | undefined>();

  // 模拟任务数据
  const mockTask: TaskBasic = {
    id: 1,
    name: '示例任务',
    group_id: '1',
    group_name: '测试分组',
    status: 'enabled',
    start_time: '09:00:00',
    end_time: '18:00:00',
    weekday: ['1', '2', '3', '4', '5'],
    frequency: { value: 30, unit: '分' },
    retry_num: '3',
    retry_frequency: { value: 5, unit: '分钟' },
    alert_task_id: [],
    alert_send_id: [],
    db_connection_id: '',
    other_info_id: '',
    create_time: '2024-01-01T00:00:00Z',
    update_time: '2024-01-01T00:00:00Z',
  };

  const handleNewTask = () => {
    setEditingTask(undefined);
    setDrawerVisible(true);
  };

  const handleEditTask = () => {
    setEditingTask(mockTask);
    setDrawerVisible(true);
  };

  const handleSubmit = () => {
    message.success('任务保存成功！');
    setDrawerVisible(false);
    setEditingTask(undefined);
  };

  const handleCancel = () => {
    setDrawerVisible(false);
    setEditingTask(undefined);
  };

  const handleReset = () => {
    message.info('表单已重置');
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>ComplexTaskForm 组件示例</h2>
      <p>这个示例展示了重构后的复合任务表单组件的使用方法。</p>

      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleNewTask}
          style={{ marginRight: 8 }}
        >
          新增任务
        </Button>
        <Button icon={<EditOutlined />} onClick={handleEditTask}>
          编辑示例任务
        </Button>
      </div>

      <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
        <h3>功能特性：</h3>
        <ul>
          <li>✅ 多标签页设计，清晰分离不同配置</li>
          <li>✅ 完整的告警配置管理（新增、编辑、删除、选择）</li>
          <li>✅ 数据库连接配置（MySQL/Oracle支持）</li>
          <li>✅ 告警发送配置（Kafka/Prometheus支持）</li>
          <li>✅ 附加信息配置（业务系统、主机信息）</li>
          <li>✅ 响应式布局，适配不同屏幕尺寸</li>
          <li>✅ 完整的表单验证和错误处理</li>
          <li>✅ 美观的图标和动画效果</li>
          <li>✅ 模块化设计，易于扩展和维护</li>
        </ul>
      </div>

      <Drawer
        title={editingTask ? '编辑任务' : '新增任务'}
        width="90%"
        open={drawerVisible}
        onClose={handleCancel}
        maskClosable={false}
        destroyOnClose
      >
        <ComplexTaskForm
          initialData={editingTask}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          onReset={handleReset}
        />
      </Drawer>
    </div>
  );
};

export default ComplexTaskFormExample;
